/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-black: #000000;
    --secondary-black: #1a1a1a;
    --dark-gray: #2d2d2d;
    --medium-gray: #4a4a4a;
    --light-gray: #6a6a6a;
    --lighter-gray: #8a8a8a;
    --lightest-gray: #e0e0e0;
    --pure-white: #ffffff;
    --off-white: #f8f8f8;
    
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --border-radius: 12px;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--secondary-black);
    background-color: var(--pure-white);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--lightest-gray);
    z-index: 1000;
    transition: var(--transition);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h2 {
    color: var(--primary-black);
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--medium-gray);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-black);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--primary-black);
}

/* Dropdown */
.dropdown {
    position: relative;
}

.dropdown-content {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--pure-white);
    min-width: 160px;
    box-shadow: var(--shadow-medium);
    border-radius: var(--border-radius);
    padding: 10px 0;
    border: 1px solid var(--lightest-gray);
}

.dropdown:hover .dropdown-content {
    display: block;
}

.dropdown-content a {
    display: block;
    padding: 10px 20px;
    text-decoration: none;
    color: var(--medium-gray);
    transition: var(--transition);
}

.dropdown-content a:hover {
    background: var(--off-white);
    color: var(--primary-black);
}

/* Mobile Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--primary-black);
    margin: 3px 0;
    transition: var(--transition);
}

/* ===== HERO SECTION ===== */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, var(--off-white) 0%, var(--pure-white) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.5rem;
    font-weight: 400;
    color: var(--medium-gray);
    margin-bottom: 1.5rem;
}

.hero-description {
    font-size: 1.1rem;
    color: var(--light-gray);
    margin-bottom: 2.5rem;
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 14px 28px;
    border: none;
    border-radius: var(--border-radius);
    font-family: var(--font-family);
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--primary-black);
    color: var(--pure-white);
    box-shadow: var(--shadow-light);
}

.btn-primary:hover {
    background: var(--secondary-black);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-black);
    border: 2px solid var(--primary-black);
}

.btn-secondary:hover {
    background: var(--primary-black);
    color: var(--pure-white);
    transform: translateY(-2px);
}

.chatbot-btn {
    flex-direction: column;
    padding: 20px 30px;
}

.btn-note {
    font-size: 0.75rem;
    font-weight: 400;
    opacity: 0.8;
    margin-top: 4px;
}

.btn-icon {
    font-size: 1.2rem;
}

/* ===== CHATBOT PREVIEW ===== */
.chatbot-preview {
    background: var(--pure-white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    overflow: hidden;
    max-width: 400px;
    margin: 0 auto;
    border: 1px solid var(--lightest-gray);
}

.chat-header {
    background: var(--primary-black);
    color: var(--pure-white);
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.chat-avatar {
    width: 40px;
    height: 40px;
    background: var(--pure-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.chat-info h4 {
    margin: 0;
    font-size: 1.1rem;
}

.status {
    font-size: 0.85rem;
    opacity: 0.8;
}

.chat-messages {
    padding: 20px;
    height: 200px;
    overflow-y: auto;
    background: var(--off-white);
}

.message {
    margin-bottom: 15px;
    display: flex;
}

.message p {
    padding: 12px 16px;
    border-radius: 18px;
    max-width: 80%;
    margin: 0;
    font-size: 0.9rem;
}

.bot-message p {
    background: var(--pure-white);
    color: var(--secondary-black);
    border: 1px solid var(--lightest-gray);
}

.user-message {
    justify-content: flex-end;
}

.user-message p {
    background: var(--primary-black);
    color: var(--pure-white);
}

.chat-input {
    padding: 15px 20px;
    background: var(--pure-white);
    border-top: 1px solid var(--lightest-gray);
    display: flex;
    gap: 10px;
}

.chat-input input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid var(--lightest-gray);
    border-radius: 20px;
    font-family: var(--font-family);
    background: var(--off-white);
}

.chat-input button {
    padding: 10px 20px;
    background: var(--medium-gray);
    color: var(--pure-white);
    border: none;
    border-radius: 20px;
    cursor: not-allowed;
    font-family: var(--font-family);
}

/* ===== FEATURES SECTION ===== */
.features {
    padding: 80px 0;
    background: var(--pure-white);
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: 3rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--off-white);
    padding: 2.5rem 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    transition: var(--transition);
    border: 1px solid var(--lightest-gray);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
    background: var(--pure-white);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.feature-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-black);
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--light-gray);
    line-height: 1.6;
}

/* ===== CONTACT SECTION ===== */
.contact {
    padding: 80px 0;
    background: var(--off-white);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
}

.contact-info h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-black);
    margin-bottom: 1rem;
}

.contact-info p {
    color: var(--light-gray);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--medium-gray);
}

.contact-icon {
    font-size: 1.2rem;
}

.contact-form {
    background: var(--pure-white);
    padding: 2.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--lightest-gray);
}

.contact-form form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-form input,
.contact-form textarea {
    padding: 15px;
    border: 1px solid var(--lightest-gray);
    border-radius: var(--border-radius);
    font-family: var(--font-family);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--off-white);
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: var(--primary-black);
    background: var(--pure-white);
}

/* ===== FOOTER ===== */
.footer {
    background: var(--primary-black);
    color: var(--pure-white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--pure-white);
}

.footer-section p {
    color: var(--lighter-gray);
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--lighter-gray);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--pure-white);
}

.footer-bottom {
    border-top: 1px solid var(--dark-gray);
    padding-top: 1rem;
    text-align: center;
    color: var(--lighter-gray);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--pure-white);
        width: 100%;
        text-align: center;
        transition: var(--transition);
        box-shadow: var(--shadow-medium);
        padding: 2rem 0;
        border-top: 1px solid var(--lightest-gray);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 100px 0 60px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .btn {
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    .chatbot-preview {
        max-width: 100%;
    }

    .contact-form {
        padding: 1.5rem;
    }
}
