// ===== AUTHENTICATION FUNCTIONALITY =====
document.addEventListener('DOMContentLoaded', function() {
    
    // ===== PASSWORD TOGGLE FUNCTIONALITY =====
    const passwordToggle = document.getElementById('passwordToggle');
    const passwordInput = document.getElementById('password');
    
    if (passwordToggle && passwordInput) {
        passwordToggle.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            const eyeIcon = this.querySelector('.eye-icon');
            eyeIcon.textContent = type === 'password' ? '👁️' : '🙈';
        });
    }
    
    // ===== ADMIN LOGIN FORM =====
    const adminLoginForm = document.getElementById('adminLoginForm');
    if (adminLoginForm) {
        adminLoginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = this.querySelector('#email').value;
            const password = this.querySelector('#password').value;
            const rememberMe = this.querySelector('#rememberMe').checked;
            
            // Basic validation
            if (!email || !password) {
                showNotification('Please fill in all fields!', 'error');
                return;
            }
            
            if (!isValidEmail(email)) {
                showNotification('Please enter a valid email address!', 'error');
                return;
            }
            
            // Simulate login process
            showNotification('Logging in...', 'info');
            
            // Simulate API call delay
            setTimeout(() => {
                // For demo purposes, accept any email/password
                if (email.includes('admin')) {
                    showNotification('Login successful! Redirecting to dashboard...', 'success');
                    
                    // Store login state (for demo)
                    if (rememberMe) {
                        localStorage.setItem('adminLoggedIn', 'true');
                        localStorage.setItem('adminEmail', email);
                    } else {
                        sessionStorage.setItem('adminLoggedIn', 'true');
                        sessionStorage.setItem('adminEmail', email);
                    }
                    
                    // Redirect to dashboard after delay
                    setTimeout(() => {
                        window.location.href = 'admin-dashboard.html';
                    }, 1500);
                } else {
                    showNotification('Invalid admin credentials!', 'error');
                }
            }, 1000);
        });
    }
    
    // ===== STUDENT LOGIN FORM =====
    const studentLoginForm = document.getElementById('studentLoginForm');
    if (studentLoginForm) {
        studentLoginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const studentId = this.querySelector('#studentId').value;
            const password = this.querySelector('#password').value;
            const rememberMe = this.querySelector('#rememberMe').checked;
            
            // Basic validation
            if (!studentId || !password) {
                showNotification('Please fill in all fields!', 'error');
                return;
            }
            
            // Simulate login process
            showNotification('Logging in...', 'info');
            
            setTimeout(() => {
                // For demo purposes, accept any student ID/password
                showNotification('Login successful! Student dashboard coming soon...', 'success');
                
                // Store login state (for demo)
                if (rememberMe) {
                    localStorage.setItem('studentLoggedIn', 'true');
                    localStorage.setItem('studentId', studentId);
                } else {
                    sessionStorage.setItem('studentLoggedIn', 'true');
                    sessionStorage.setItem('studentId', studentId);
                }
                
                // For now, redirect back to home since student dashboard isn't built yet
                setTimeout(() => {
                    showNotification('Student dashboard will be available in Phase 2!', 'info');
                }, 2000);
            }, 1000);
        });
    }
    
    // ===== GOOGLE LOGIN BUTTON =====
    const googleLoginBtn = document.getElementById('googleLogin');
    if (googleLoginBtn) {
        googleLoginBtn.addEventListener('click', function() {
            showNotification('Google login will be integrated with Firebase in Phase 2!', 'info');
        });
    }
    
    // ===== DEMO LOGIN BUTTON =====
    const demoLoginBtn = document.getElementById('demoLogin');
    if (demoLoginBtn) {
        demoLoginBtn.addEventListener('click', function() {
            showNotification('Loading demo dashboard...', 'info');
            
            // Set demo login state
            sessionStorage.setItem('adminLoggedIn', 'true');
            sessionStorage.setItem('adminEmail', '<EMAIL>');
            sessionStorage.setItem('demoMode', 'true');
            
            setTimeout(() => {
                window.location.href = 'admin-dashboard.html';
            }, 1000);
        });
    }
    
    // ===== GUEST MODE BUTTON =====
    const guestModeBtn = document.getElementById('guestMode');
    if (guestModeBtn) {
        guestModeBtn.addEventListener('click', function() {
            showNotification('Redirecting to home page as guest...', 'info');
            setTimeout(() => {
                window.location.href = '../index.html';
            }, 1000);
        });
    }
    
    // ===== FORGOT PASSWORD LINKS =====
    const forgotPasswordLinks = document.querySelectorAll('.forgot-password');
    forgotPasswordLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            showNotification('Password reset functionality will be available in Phase 2!', 'info');
        });
    });
    
    // ===== FORM ANIMATIONS =====
    const formInputs = document.querySelectorAll('.form-group input');
    formInputs.forEach(input => {
        // Add focus animations
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
        
        // Check if input has value on page load
        if (input.value) {
            input.parentElement.classList.add('focused');
        }
    });
    
    // ===== LOADING STATES =====
    function setButtonLoading(button, isLoading) {
        if (isLoading) {
            button.disabled = true;
            button.style.opacity = '0.7';
            button.style.cursor = 'not-allowed';
            
            const originalText = button.innerHTML;
            button.setAttribute('data-original-text', originalText);
            button.innerHTML = '<span style="margin-right: 10px;">⏳</span>Loading...';
        } else {
            button.disabled = false;
            button.style.opacity = '1';
            button.style.cursor = 'pointer';
            
            const originalText = button.getAttribute('data-original-text');
            if (originalText) {
                button.innerHTML = originalText;
            }
        }
    }
    
    // ===== FORM VALIDATION HELPERS =====
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    function validateForm(formData) {
        const errors = [];
        
        // Check required fields
        for (let [key, value] of formData.entries()) {
            if (!value.trim()) {
                errors.push(`${key} is required`);
            }
        }
        
        return errors;
    }
    
    // ===== PAGE LOAD ANIMATIONS =====
    const authCard = document.querySelector('.auth-form-card');
    const authInfo = document.querySelector('.auth-info-content');
    
    if (authCard) {
        authCard.style.opacity = '0';
        authCard.style.transform = 'translateY(30px)';
        authCard.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
        
        setTimeout(() => {
            authCard.style.opacity = '1';
            authCard.style.transform = 'translateY(0)';
        }, 200);
    }
    
    if (authInfo) {
        authInfo.style.opacity = '0';
        authInfo.style.transform = 'translateX(-30px)';
        authInfo.style.transition = 'opacity 0.8s ease 0.3s, transform 0.8s ease 0.3s';
        
        setTimeout(() => {
            authInfo.style.opacity = '1';
            authInfo.style.transform = 'translateX(0)';
        }, 500);
    }
    
    // ===== FEATURE CARDS ANIMATION =====
    const featureCards = document.querySelectorAll('.auth-feature');
    featureCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateX(-20px)';
        card.style.transition = `opacity 0.6s ease ${0.1 * index + 0.5}s, transform 0.6s ease ${0.1 * index + 0.5}s`;
        
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateX(0)';
        }, 100 * index + 600);
    });
});

// ===== UTILITY FUNCTIONS =====
function showNotification(message, type = 'info') {
    // This function is defined in script.js
    // We're calling it here for consistency
    if (typeof window.showNotification === 'function') {
        window.showNotification(message, type);
    } else {
        // Fallback alert if main script isn't loaded
        alert(message);
    }
}

// ===== CHECK LOGIN STATE =====
function checkLoginState() {
    const adminLoggedIn = localStorage.getItem('adminLoggedIn') || sessionStorage.getItem('adminLoggedIn');
    const studentLoggedIn = localStorage.getItem('studentLoggedIn') || sessionStorage.getItem('studentLoggedIn');
    
    return {
        admin: adminLoggedIn === 'true',
        student: studentLoggedIn === 'true'
    };
}

// ===== LOGOUT FUNCTIONALITY =====
function logout() {
    // Clear all login states
    localStorage.removeItem('adminLoggedIn');
    localStorage.removeItem('adminEmail');
    localStorage.removeItem('studentLoggedIn');
    localStorage.removeItem('studentId');
    localStorage.removeItem('demoMode');
    
    sessionStorage.removeItem('adminLoggedIn');
    sessionStorage.removeItem('adminEmail');
    sessionStorage.removeItem('studentLoggedIn');
    sessionStorage.removeItem('studentId');
    sessionStorage.removeItem('demoMode');
    
    showNotification('Logged out successfully!', 'success');
    
    setTimeout(() => {
        window.location.href = '../index.html';
    }, 1000);
}

// Make functions available globally
window.checkLoginState = checkLoginState;
window.logout = logout;
