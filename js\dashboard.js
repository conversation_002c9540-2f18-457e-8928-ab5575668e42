// ===== DASHBOARD FUNCTIONALITY =====
document.addEventListener('DOMContentLoaded', function() {
    
    // ===== CHECK LOGIN STATE =====
    const loginState = checkLoginState();
    if (!loginState.admin) {
        showNotification('Please login to access the dashboard!', 'error');
        setTimeout(() => {
            window.location.href = 'admin-login.html';
        }, 2000);
        return;
    }
    
    // ===== SIDEBAR FUNCTIONALITY =====
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const sidebarOverlay = document.getElementById('sidebarOverlay');
    
    // Desktop sidebar toggle
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            
            // Save sidebar state
            const isCollapsed = sidebar.classList.contains('collapsed');
            localStorage.setItem('sidebarCollapsed', isCollapsed);
        });
    }
    
    // Mobile menu toggle
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('mobile-open');
            sidebarOverlay.classList.toggle('active');
        });
    }
    
    // Close mobile menu when clicking overlay
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', function() {
            sidebar.classList.remove('mobile-open');
            sidebarOverlay.classList.remove('active');
        });
    }
    
    // Restore sidebar state
    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed');
    if (sidebarCollapsed === 'true') {
        sidebar.classList.add('collapsed');
    }
    
    // ===== NAVIGATION FUNCTIONALITY =====
    const navLinks = document.querySelectorAll('.nav-link');
    const pageTitle = document.querySelector('.page-title');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Add active class to clicked item
            this.parentElement.classList.add('active');
            
            // Update page title
            const navText = this.querySelector('.nav-text').textContent;
            pageTitle.textContent = navText;
            
            // Close mobile menu if open
            sidebar.classList.remove('mobile-open');
            sidebarOverlay.classList.remove('active');
            
            // Handle navigation
            const href = this.getAttribute('href');
            handleNavigation(href, navText);
        });
    });
    
    // ===== NAVIGATION HANDLER =====
    function handleNavigation(href, title) {
        const dashboardContent = document.querySelector('.dashboard-content');
        
        // Show loading state
        dashboardContent.style.opacity = '0.5';
        showNotification(`Loading ${title}...`, 'info');
        
        setTimeout(() => {
            dashboardContent.style.opacity = '1';
            
            switch(href) {
                case '#dashboard':
                    showNotification('Dashboard loaded!', 'success');
                    break;
                case '#students':
                    showNotification('Student management will be available in Phase 3!', 'info');
                    break;
                case '#data':
                    showNotification('Data management will be available in Phase 3!', 'info');
                    break;
                case '#chatbot':
                    showNotification('Chatbot settings will be available in Phase 4!', 'info');
                    break;
                case '#analytics':
                    showNotification('Analytics will be available in Phase 3!', 'info');
                    break;
                case '#settings':
                    showNotification('Settings will be available in Phase 3!', 'info');
                    break;
                default:
                    showNotification('Feature coming soon!', 'info');
            }
        }, 800);
    }
    
    // ===== ACTION CARDS FUNCTIONALITY =====
    const actionCards = document.querySelectorAll('.action-card');
    actionCards.forEach(card => {
        card.addEventListener('click', function() {
            const action = this.getAttribute('data-action');
            handleActionCard(action);
        });
    });
    
    function handleActionCard(action) {
        switch(action) {
            case 'add-student':
                showNotification('Add Student feature will be available in Phase 3!', 'info');
                break;
            case 'manage-data':
                showNotification('Data Management will be available in Phase 3!', 'info');
                break;
            case 'view-analytics':
                showNotification('Analytics Dashboard will be available in Phase 3!', 'info');
                break;
            case 'chatbot-settings':
                showNotification('Chatbot Configuration will be available in Phase 4!', 'info');
                break;
            default:
                showNotification('Feature coming soon!', 'info');
        }
    }
    
    // ===== TOP NAV ACTIONS =====
    const actionBtns = document.querySelectorAll('.action-btn');
    actionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const icon = this.querySelector('.action-icon').textContent;
            
            switch(icon) {
                case '🔔':
                    showNotification('Notifications feature coming in Phase 3!', 'info');
                    break;
                case '💬':
                    showNotification('Messages feature coming in Phase 3!', 'info');
                    break;
                case '❓':
                    showNotification('Help center coming soon!', 'info');
                    break;
            }
        });
    });
    
    // ===== LOGOUT FUNCTIONALITY =====
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to logout?')) {
                logout();
            }
        });
    }
    
    // ===== DEMO DATA UPDATES =====
    function updateDemoData() {
        // Simulate real-time data updates
        const statValues = document.querySelectorAll('.stat-value');
        const welcomeStats = document.querySelectorAll('.stat-number');
        
        setInterval(() => {
            // Update random stat values slightly
            statValues.forEach(stat => {
                if (stat.textContent.includes('%')) {
                    const currentValue = parseFloat(stat.textContent);
                    const newValue = currentValue + (Math.random() - 0.5) * 0.2;
                    stat.textContent = newValue.toFixed(1) + '%';
                } else if (stat.textContent.includes('s')) {
                    const currentValue = parseFloat(stat.textContent);
                    const newValue = currentValue + (Math.random() - 0.5) * 0.1;
                    stat.textContent = newValue.toFixed(1) + 's';
                }
            });
            
            // Update welcome stats
            welcomeStats.forEach(stat => {
                if (!stat.textContent.includes('%') && !stat.textContent.includes('s')) {
                    const currentValue = parseInt(stat.textContent.replace(/,/g, ''));
                    const change = Math.floor((Math.random() - 0.5) * 10);
                    const newValue = Math.max(0, currentValue + change);
                    stat.textContent = newValue.toLocaleString();
                }
            });
        }, 30000); // Update every 30 seconds
    }
    
    // Start demo data updates
    updateDemoData();
    
    // ===== DISPLAY USER INFO =====
    function displayUserInfo() {
        const adminEmail = localStorage.getItem('adminEmail') || sessionStorage.getItem('adminEmail');
        const isDemoMode = sessionStorage.getItem('demoMode') === 'true';
        
        const profileInfo = document.querySelector('.profile-info');
        if (profileInfo && adminEmail) {
            const nameElement = profileInfo.querySelector('h4');
            const roleElement = profileInfo.querySelector('p');
            
            if (isDemoMode) {
                nameElement.textContent = 'Demo Admin';
                roleElement.textContent = 'Demo Mode';
            } else {
                nameElement.textContent = adminEmail.split('@')[0];
                roleElement.textContent = 'Administrator';
            }
        }
    }
    
    displayUserInfo();
    
    // ===== KEYBOARD SHORTCUTS =====
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + B to toggle sidebar
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            if (sidebarToggle) {
                sidebarToggle.click();
            }
        }
        
        // Escape to close mobile menu
        if (e.key === 'Escape') {
            sidebar.classList.remove('mobile-open');
            sidebarOverlay.classList.remove('active');
        }
    });
    
    // ===== RESPONSIVE BEHAVIOR =====
    function handleResize() {
        const width = window.innerWidth;
        
        if (width <= 768) {
            // Mobile: ensure sidebar is closed and not collapsed
            sidebar.classList.remove('collapsed');
            sidebar.classList.remove('mobile-open');
            sidebarOverlay.classList.remove('active');
        } else {
            // Desktop: restore sidebar state
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed');
            if (sidebarCollapsed === 'true') {
                sidebar.classList.add('collapsed');
            }
        }
    }
    
    window.addEventListener('resize', debounce(handleResize, 250));
    handleResize(); // Initial call
    
    // ===== ACTIVITY UPDATES =====
    function addActivityItem(icon, message, time = 'Just now') {
        const activityList = document.querySelector('.activity-list');
        if (!activityList) return;
        
        const activityItem = document.createElement('div');
        activityItem.className = 'activity-item';
        activityItem.innerHTML = `
            <div class="activity-icon">${icon}</div>
            <div class="activity-content">
                <p>${message}</p>
                <span class="activity-time">${time}</span>
            </div>
        `;
        
        // Add to top of list
        activityList.insertBefore(activityItem, activityList.firstChild);
        
        // Remove last item if more than 4 items
        const items = activityList.querySelectorAll('.activity-item');
        if (items.length > 4) {
            activityList.removeChild(items[items.length - 1]);
        }
        
        // Animate new item
        activityItem.style.opacity = '0';
        activityItem.style.transform = 'translateX(-20px)';
        setTimeout(() => {
            activityItem.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            activityItem.style.opacity = '1';
            activityItem.style.transform = 'translateX(0)';
        }, 100);
    }
    
    // Simulate activity updates
    const activities = [
        { icon: '👤', message: 'New student inquiry received' },
        { icon: '💬', message: 'Chatbot handled 15 queries in the last hour' },
        { icon: '📊', message: 'Weekly report generated successfully' },
        { icon: '⚙️', message: 'System backup completed' }
    ];
    
    // Add random activity every 2 minutes
    setInterval(() => {
        const randomActivity = activities[Math.floor(Math.random() * activities.length)];
        addActivityItem(randomActivity.icon, randomActivity.message);
    }, 120000);
    
    // ===== WELCOME MESSAGE =====
    const isDemoMode = sessionStorage.getItem('demoMode') === 'true';
    if (isDemoMode) {
        setTimeout(() => {
            showNotification('Welcome to EduBot Admin Dashboard! This is a demo version.', 'info');
        }, 1000);
    }
});

// ===== UTILITY FUNCTIONS =====
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// ===== EXPORT FUNCTIONS =====
window.handleNavigation = function(href, title) {
    // This allows external scripts to trigger navigation
    const event = new CustomEvent('navigate', { detail: { href, title } });
    document.dispatchEvent(event);
};
