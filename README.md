# EduBot - College Chatbot Frontend

A modern, monochrome frontend for a college-focused chatbot web application that assists students with various college-related queries.

## 🎯 Current Phase: Frontend UI (Phase 1)

This is **Phase 1** of the EduBot project, focusing solely on creating a clean, responsive frontend UI with no backend functionality.

## 📁 Project Structure

```
ChatBot/
├── index.html              # Home page
├── pages/
│   ├── admin-login.html    # Admin login page
│   ├── student-login.html  # Student login page
│   └── admin-dashboard.html # Admin dashboard template
├── css/
│   ├── style.css          # Main styles
│   ├── auth.css           # Authentication pages styles
│   └── dashboard.css      # Dashboard styles
├── js/
│   ├── script.js          # Main JavaScript
│   ├── auth.js            # Authentication functionality
│   └── dashboard.js       # Dashboard functionality
├── assets/                # Images and other assets (empty for now)
└── README.md              # This file
```

## 🎨 Design Features

- **Monochrome Theme**: Clean black and white design
- **Responsive Layout**: Works on desktop, tablet, and mobile
- **Modern UI Elements**: Cards, shadows, smooth transitions
- **Interactive Components**: Hover effects, animations, notifications
- **Professional Typography**: Inter font family

## 📄 Pages Included

### 1. Home Page (`index.html`)
- Hero section with EduBot introduction
- Feature showcase
- Chatbot preview (UI only)
- Contact form
- Navigation to login pages

### 2. Admin Login (`pages/admin-login.html`)
- Clean login form with email/password
- Demo dashboard access
- Responsive design
- Form validation (frontend only)

### 3. Student Login (`pages/student-login.html`)
- Google login button (UI only)
- Traditional login form
- Guest mode option
- Mobile-friendly design

### 4. Admin Dashboard (`pages/admin-dashboard.html`)
- Sidebar navigation
- Statistics cards
- Quick action buttons
- Recent activity feed
- Responsive sidebar collapse

## 🚀 Getting Started

1. **Clone or download** this repository
2. **Open `index.html`** in your web browser
3. **Navigate** through the different pages using the navigation

### Demo Access
- **Admin Dashboard**: Use the "View Demo Dashboard" button on admin login
- **Forms**: All forms show notifications but don't submit data
- **Buttons**: Most buttons show "coming soon" notifications

## 🔧 Technical Details

### Technologies Used
- **HTML5**: Semantic markup
- **CSS3**: Modern styling with CSS Grid and Flexbox
- **Vanilla JavaScript**: No frameworks, pure JS
- **Google Fonts**: Inter font family

### Key Features
- Fully responsive design
- Smooth animations and transitions
- Form validation (frontend only)
- Local storage for demo login states
- Notification system
- Mobile-first approach

## 📱 Responsive Breakpoints

- **Desktop**: 1024px and above
- **Tablet**: 768px - 1023px
- **Mobile**: Below 768px

## 🎯 Future Phases

### Phase 2: Firebase Integration
- Google Firebase Authentication
- Firestore database integration
- Real-time data updates

### Phase 3: Admin Panel Functions
- Student data management
- CRUD operations
- Analytics dashboard
- System settings

### Phase 4: Chatbot Integration
- AI chatbot integration (Together.ai or similar)
- Natural language processing
- Smart response system

### Phase 5: Student Features
- Personal student dashboard
- Private data access after login
- Personalized chatbot responses

## 🎨 Color Scheme

```css
--primary-black: #000000
--secondary-black: #1a1a1a
--dark-gray: #2d2d2d
--medium-gray: #4a4a4a
--light-gray: #6a6a6a
--lighter-gray: #8a8a8a
--lightest-gray: #e0e0e0
--pure-white: #ffffff
--off-white: #f8f8f8
```

## 📝 Notes

- **No Backend**: This is frontend only - all forms and buttons are for UI demonstration
- **Demo Data**: Statistics and activity feeds show simulated data
- **Notifications**: Custom notification system for user feedback
- **Login States**: Stored in localStorage/sessionStorage for demo purposes

## 🔍 Testing

To test the application:

1. **Home Page**: Check responsive design and navigation
2. **Login Pages**: Try form validation and demo login
3. **Dashboard**: Test sidebar collapse and responsive behavior
4. **Mobile**: Test on different screen sizes

## 🤝 Contributing

This is Phase 1 of the project. Future phases will add:
- Backend integration
- Database connectivity
- AI chatbot functionality
- Advanced admin features

---

**Built with ❤️ for students** | EduBot Frontend v1.0
