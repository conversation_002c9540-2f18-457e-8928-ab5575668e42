<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Login - EduBot</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/auth.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="auth-body">
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="../index.html">
                    <h2>EduBot</h2>
                </a>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="student-login.html" class="nav-link active">Student Login</a>
                </li>
                <li class="nav-item">
                    <a href="admin-login.html" class="nav-link">Admin Login</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="auth-main">
        <div class="auth-container">
            <!-- Left Side - Info -->
            <div class="auth-info">
                <div class="auth-info-content">
                    <h1>Student Portal</h1>
                    <p>Access your personal academic information and chat with EduBot about your college queries.</p>
                    
                    <div class="auth-features">
                        <div class="auth-feature">
                            <span class="feature-icon">💰</span>
                            <div>
                                <h3>Fee Information</h3>
                                <p>Check your pending fees, payment history, and due dates</p>
                            </div>
                        </div>
                        <div class="auth-feature">
                            <span class="feature-icon">📊</span>
                            <div>
                                <h3>Academic Records</h3>
                                <p>View your marks, grades, and academic performance</p>
                            </div>
                        </div>
                        <div class="auth-feature">
                            <span class="feature-icon">📅</span>
                            <div>
                                <h3>Personal Schedule</h3>
                                <p>Access your class timetable and exam schedules</p>
                            </div>
                        </div>
                        <div class="auth-feature">
                            <span class="feature-icon">🤖</span>
                            <div>
                                <h3>Personal Assistant</h3>
                                <p>Chat with EduBot about your specific academic queries</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="auth-form-container">
                <div class="auth-form-card">
                    <div class="auth-header">
                        <h2>Student Login</h2>
                        <p>Sign in to access your personal dashboard</p>
                    </div>

                    <!-- Google Login (Primary) -->
                    <div class="auth-primary-login">
                        <button class="btn btn-google" id="googleLogin">
                            <span class="google-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24">
                                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                </svg>
                            </span>
                            Continue with Google
                            <span class="btn-note">(UI Only - No Functionality Yet)</span>
                        </button>
                    </div>

                    <div class="auth-divider">
                        <span>or</span>
                    </div>

                    <!-- Traditional Login Form -->
                    <form class="auth-form" id="studentLoginForm">
                        <div class="form-group">
                            <label for="studentId">Student ID</label>
                            <input 
                                type="text" 
                                id="studentId" 
                                name="studentId" 
                                placeholder="Enter your student ID"
                                required
                            >
                        </div>

                        <div class="form-group">
                            <label for="password">Password</label>
                            <div class="password-input">
                                <input 
                                    type="password" 
                                    id="password" 
                                    name="password" 
                                    placeholder="Enter your password"
                                    required
                                >
                                <button type="button" class="password-toggle" id="passwordToggle">
                                    <span class="eye-icon">👁️</span>
                                </button>
                            </div>
                        </div>

                        <div class="form-options">
                            <label class="checkbox-container">
                                <input type="checkbox" id="rememberMe">
                                <span class="checkmark"></span>
                                Remember me
                            </label>
                            <a href="#" class="forgot-password">Forgot password?</a>
                        </div>

                        <button type="submit" class="btn btn-primary auth-submit">
                            Sign In
                            <span class="btn-note">(UI Only - No Functionality Yet)</span>
                        </button>
                    </form>

                    <div class="auth-alternatives">
                        <button class="btn btn-secondary auth-alt-btn" id="guestMode">
                            <span class="btn-icon">👤</span>
                            Continue as Guest
                        </button>
                    </div>

                    <div class="auth-footer">
                        <p>New student? <a href="#">Register Here</a></p>
                        <p>Are you an admin? <a href="admin-login.html">Admin Login</a></p>
                        <p>Need help? <a href="../index.html#contact">Contact Support</a></p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Background Elements -->
    <div class="auth-bg-elements">
        <div class="bg-circle bg-circle-1"></div>
        <div class="bg-circle bg-circle-2"></div>
        <div class="bg-circle bg-circle-3"></div>
    </div>

    <script src="../js/script.js"></script>
    <script src="../js/auth.js"></script>
</body>
</html>
