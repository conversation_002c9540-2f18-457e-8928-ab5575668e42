/* ===== DASHBOARD LAYOUT ===== */
.dashboard-body {
    background: var(--off-white);
    display: flex;
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== SIDEBAR ===== */
.sidebar {
    width: 280px;
    background: var(--primary-black);
    color: var(--pure-white);
    display: flex;
    flex-direction: column;
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transition: var(--transition);
    border-right: 1px solid var(--dark-gray);
}

.sidebar.collapsed {
    width: 80px;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--dark-gray);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-logo h2 {
    color: var(--pure-white);
    font-size: 1.5rem;
    margin: 0;
}

.admin-badge {
    background: var(--dark-gray);
    color: var(--lighter-gray);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.25rem;
    display: block;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--pure-white);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: var(--dark-gray);
}

.toggle-icon {
    font-size: 1.2rem;
    transition: var(--transition);
}

.sidebar.collapsed .toggle-icon {
    transform: rotate(180deg);
}

/* ===== SIDEBAR NAVIGATION ===== */
.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
    overflow-y: auto;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin: 0.25rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: var(--lighter-gray);
    text-decoration: none;
    transition: var(--transition);
    border-radius: 0;
    position: relative;
}

.nav-link:hover {
    background: var(--dark-gray);
    color: var(--pure-white);
}

.nav-item.active .nav-link {
    background: var(--dark-gray);
    color: var(--pure-white);
    border-right: 3px solid var(--pure-white);
}

.nav-icon {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.nav-text {
    font-weight: 500;
    transition: var(--transition);
}

.sidebar.collapsed .nav-text,
.sidebar.collapsed .admin-badge {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

/* ===== SIDEBAR FOOTER ===== */
.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--dark-gray);
}

.admin-profile {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--dark-gray);
    border-radius: var(--border-radius);
}

.profile-avatar {
    width: 40px;
    height: 40px;
    background: var(--medium-gray);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.profile-info h4 {
    margin: 0;
    font-size: 0.9rem;
    color: var(--pure-white);
}

.profile-info p {
    margin: 0;
    font-size: 0.75rem;
    color: var(--lighter-gray);
}

.logout-btn {
    width: 100%;
    background: transparent;
    border: 1px solid var(--dark-gray);
    color: var(--lighter-gray);
    padding: 0.75rem;
    font-size: 0.9rem;
}

.logout-btn:hover {
    background: var(--dark-gray);
    color: var(--pure-white);
    border-color: var(--medium-gray);
}

/* ===== MAIN CONTENT ===== */
.main-content {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    transition: var(--transition);
}

.sidebar.collapsed + .main-content {
    margin-left: 80px;
}

/* ===== TOP NAVIGATION ===== */
.top-nav {
    background: var(--pure-white);
    border-bottom: 1px solid var(--lightest-gray);
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-light);
}

.top-nav-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    flex-direction: column;
    gap: 3px;
    padding: 0.5rem;
}

.hamburger-line {
    width: 20px;
    height: 2px;
    background: var(--primary-black);
    transition: var(--transition);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-black);
    margin: 0;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn {
    background: none;
    border: none;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.action-btn:hover {
    background: var(--off-white);
}

.action-icon {
    font-size: 1.2rem;
}

.notification-badge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    background: var(--primary-black);
    color: var(--pure-white);
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

/* ===== DASHBOARD CONTENT ===== */
.dashboard-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

/* ===== WELCOME SECTION ===== */
.welcome-section {
    margin-bottom: 2rem;
}

.welcome-card {
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--secondary-black) 100%);
    color: var(--pure-white);
    padding: 2.5rem;
    border-radius: var(--border-radius);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-medium);
}

.welcome-content h2 {
    font-size: 1.8rem;
    margin: 0 0 0.5rem 0;
    font-weight: 600;
}

.welcome-content p {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.welcome-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--pure-white);
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* ===== STATS SECTION ===== */
.stats-section {
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: var(--pure-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--lightest-gray);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.stat-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat-header .stat-icon {
    font-size: 1.5rem;
}

.stat-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--medium-gray);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.9rem;
    font-weight: 500;
}

.stat-change.positive {
    color: var(--dark-gray);
}

.stat-change.negative {
    color: var(--medium-gray);
}

/* ===== SECTIONS ===== */
.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-black);
    margin-bottom: 1.5rem;
}

/* ===== ACTIONS SECTION ===== */
.actions-section {
    margin-bottom: 2rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.action-card {
    background: var(--pure-white);
    border: 1px solid var(--lightest-gray);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.action-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-black);
}

.action-card-icon {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.action-card h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-black);
}

.action-card p {
    margin: 0;
    color: var(--light-gray);
    font-size: 0.95rem;
    line-height: 1.5;
}

/* ===== ACTIVITY SECTION ===== */
.activity-section {
    margin-bottom: 2rem;
}

.activity-card {
    background: var(--pure-white);
    border: 1px solid var(--lightest-gray);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.activity-list {
    padding: 1.5rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--lightest-gray);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: var(--off-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    margin: 0 0 0.5rem 0;
    color: var(--secondary-black);
    font-size: 0.95rem;
    line-height: 1.5;
}

.activity-time {
    font-size: 0.85rem;
    color: var(--light-gray);
}

.activity-footer {
    padding: 1rem 1.5rem;
    background: var(--off-white);
    border-top: 1px solid var(--lightest-gray);
    text-align: center;
}

/* ===== SIDEBAR OVERLAY (MOBILE) ===== */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

.sidebar-overlay.active {
    display: block;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .welcome-card {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .welcome-stats {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }

    .actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: 280px;
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .sidebar.collapsed + .main-content {
        margin-left: 0;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .top-nav {
        padding: 1rem;
    }

    .dashboard-content {
        padding: 1rem;
    }

    .welcome-card {
        padding: 1.5rem;
    }

    .welcome-content h2 {
        font-size: 1.5rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .action-card {
        padding: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .actions-grid {
        grid-template-columns: 1fr;
    }

    .welcome-stats {
        gap: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .top-nav {
        padding: 0.75rem;
    }

    .page-title {
        font-size: 1.2rem;
    }

    .dashboard-content {
        padding: 0.75rem;
    }

    .welcome-card {
        padding: 1rem;
    }

    .welcome-content h2 {
        font-size: 1.3rem;
    }

    .welcome-content p {
        font-size: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-value {
        font-size: 2rem;
    }

    .action-card {
        padding: 1rem;
    }

    .action-card-icon {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.3rem;
    }

    .activity-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .activity-icon {
        align-self: center;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card,
.action-card,
.activity-card,
.welcome-card {
    animation: slideIn 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

.action-card:nth-child(1) { animation-delay: 0.1s; }
.action-card:nth-child(2) { animation-delay: 0.2s; }
.action-card:nth-child(3) { animation-delay: 0.3s; }
.action-card:nth-child(4) { animation-delay: 0.4s; }
