<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EduBot - Your Smart College Assistant</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2>EduBot</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link active">Home</a>
                </li>
                <li class="nav-item dropdown">
                    <a href="#" class="nav-link">Login</a>
                    <div class="dropdown-content">
                        <a href="pages/student-login.html">Student Login</a>
                        <a href="pages/admin-login.html">Admin Login</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">Contact</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">Welcome to EduBot</h1>
                <h2 class="hero-subtitle">Your Smart College Assistant</h2>
                <p class="hero-description">
                    Get answers to your college questions instantly. From fee details and marks to timetables and announcements - EduBot is here to help you navigate your academic journey.
                </p>
                <div class="hero-buttons">
                    <button class="btn btn-primary chatbot-btn">
                        <span class="btn-icon">💬</span>
                        Start Chatting
                        <span class="btn-note">(UI Only - No Functionality Yet)</span>
                    </button>
                    <a href="pages/student-login.html" class="btn btn-secondary">Student Login</a>
                </div>
            </div>
            <div class="hero-visual">
                <div class="chatbot-preview">
                    <div class="chat-header">
                        <div class="chat-avatar">🤖</div>
                        <div class="chat-info">
                            <h4>EduBot</h4>
                            <span class="status">Online</span>
                        </div>
                    </div>
                    <div class="chat-messages">
                        <div class="message bot-message">
                            <p>Hello! I'm EduBot. How can I help you today?</p>
                        </div>
                        <div class="message user-message">
                            <p>What are my pending fees?</p>
                        </div>
                        <div class="message bot-message">
                            <p>Let me check your fee details...</p>
                        </div>
                    </div>
                    <div class="chat-input">
                        <input type="text" placeholder="Type your message..." disabled>
                        <button disabled>Send</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <h2 class="section-title">What EduBot Can Help You With</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">💰</div>
                    <h3>Fee Information</h3>
                    <p>Check pending fees, payment history, and due dates</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>Academic Records</h3>
                    <p>View your marks, grades, and academic performance</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📅</div>
                    <h3>Timetables</h3>
                    <p>Access class schedules, exam dates, and events</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📢</div>
                    <h3>Announcements</h3>
                    <p>Stay updated with college news and notifications</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">Get in Touch</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <h3>Need Help?</h3>
                    <p>Our support team is here to assist you with any questions about EduBot.</p>
                    <div class="contact-details">
                        <div class="contact-item">
                            <span class="contact-icon">📧</span>
                            <span><EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <span class="contact-icon">📞</span>
                            <span>+1 (555) 123-4567</span>
                        </div>
                    </div>
                </div>
                <div class="contact-form">
                    <form>
                        <input type="text" placeholder="Your Name" required>
                        <input type="email" placeholder="Your Email" required>
                        <textarea placeholder="Your Message" rows="4" required></textarea>
                        <button type="submit" class="btn btn-primary">Send Message</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>EduBot</h3>
                    <p>Your Smart College Assistant</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="pages/student-login.html">Student Login</a></li>
                        <li><a href="pages/admin-login.html">Admin Login</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#contact">Contact Us</a></li>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 EduBot. All rights reserved. Built with ❤️ for students.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
</body>
</html>
