/* ===== AUTHENTICATION PAGES STYLES ===== */

.auth-body {
    background: linear-gradient(135deg, var(--off-white) 0%, var(--pure-white) 50%, var(--lightest-gray) 100%);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

.auth-main {
    padding-top: 70px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
}

.auth-container {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

/* ===== LEFT SIDE - INFO SECTION ===== */
.auth-info {
    padding: 2rem 0;
}

.auth-info-content h1 {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.auth-info-content p {
    font-size: 1.2rem;
    color: var(--light-gray);
    margin-bottom: 3rem;
    line-height: 1.6;
}

.auth-features {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.auth-feature {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.7);
    border-radius: var(--border-radius);
    border: 1px solid var(--lightest-gray);
    transition: var(--transition);
}

.auth-feature:hover {
    background: var(--pure-white);
    transform: translateX(10px);
    box-shadow: var(--shadow-light);
}

.auth-feature .feature-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.auth-feature h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-black);
    margin-bottom: 0.5rem;
}

.auth-feature p {
    color: var(--medium-gray);
    font-size: 0.95rem;
    line-height: 1.5;
    margin: 0;
}

/* ===== RIGHT SIDE - FORM SECTION ===== */
.auth-form-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.auth-form-card {
    background: var(--pure-white);
    padding: 3rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    border: 1px solid var(--lightest-gray);
    width: 100%;
    max-width: 450px;
    position: relative;
}

.auth-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.auth-header h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: var(--light-gray);
    font-size: 1rem;
}

/* ===== FORM STYLES ===== */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: var(--secondary-black);
    font-size: 0.9rem;
}

.form-group input {
    padding: 15px;
    border: 2px solid var(--lightest-gray);
    border-radius: var(--border-radius);
    font-family: var(--font-family);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--off-white);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-black);
    background: var(--pure-white);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    color: var(--medium-gray);
    transition: var(--transition);
}

.password-toggle:hover {
    color: var(--primary-black);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0.5rem 0;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--medium-gray);
}

.checkbox-container input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin: 0;
}

.forgot-password {
    color: var(--medium-gray);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.forgot-password:hover {
    color: var(--primary-black);
    text-decoration: underline;
}

.auth-submit {
    margin-top: 1rem;
    padding: 16px;
    font-size: 1.1rem;
    flex-direction: column;
    gap: 0.5rem;
}

/* ===== GOOGLE LOGIN BUTTON ===== */
.auth-primary-login {
    margin-bottom: 1.5rem;
}

.btn-google {
    width: 100%;
    background: var(--pure-white);
    color: var(--secondary-black);
    border: 2px solid var(--lightest-gray);
    padding: 16px 20px;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    transition: var(--transition);
    flex-direction: column;
}

.btn-google:hover {
    background: var(--off-white);
    border-color: var(--light-gray);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.google-icon {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ===== DIVIDER ===== */
.auth-divider {
    display: flex;
    align-items: center;
    margin: 2rem 0;
    color: var(--light-gray);
    font-size: 0.9rem;
}

.auth-divider::before,
.auth-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: var(--lightest-gray);
}

.auth-divider span {
    padding: 0 1rem;
}

/* ===== ALTERNATIVE ACTIONS ===== */
.auth-alternatives {
    margin: 1.5rem 0;
}

.auth-alt-btn {
    width: 100%;
    padding: 14px;
    justify-content: center;
}

/* ===== FOOTER LINKS ===== */
.auth-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--lightest-gray);
}

.auth-footer p {
    margin: 0.5rem 0;
    color: var(--light-gray);
    font-size: 0.9rem;
}

.auth-footer a {
    color: var(--primary-black);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* ===== BACKGROUND ELEMENTS ===== */
.auth-bg-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.bg-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.05) 0%, rgba(0, 0, 0, 0.02) 100%);
    animation: float 6s ease-in-out infinite;
}

.bg-circle-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    right: -150px;
    animation-delay: 0s;
}

.bg-circle-2 {
    width: 200px;
    height: 200px;
    bottom: 20%;
    left: -100px;
    animation-delay: 2s;
}

.bg-circle-3 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 968px) {
    .auth-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 1rem;
    }
    
    .auth-info-content h1 {
        font-size: 2.5rem;
    }
    
    .auth-features {
        gap: 1rem;
    }
    
    .auth-feature {
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .auth-form-card {
        padding: 2rem;
    }
    
    .auth-info-content h1 {
        font-size: 2rem;
    }
    
    .auth-info-content p {
        font-size: 1rem;
    }
    
    .auth-feature {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .auth-main {
        padding-top: 90px;
    }
    
    .auth-form-card {
        padding: 1.5rem;
    }
    
    .auth-header h2 {
        font-size: 1.5rem;
    }
    
    .bg-circle-1,
    .bg-circle-2,
    .bg-circle-3 {
        display: none;
    }
}
