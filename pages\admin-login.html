<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - EduBot</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/auth.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="auth-body">
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="../index.html">
                    <h2>EduBot</h2>
                </a>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="student-login.html" class="nav-link">Student Login</a>
                </li>
                <li class="nav-item">
                    <a href="admin-login.html" class="nav-link active">Admin Login</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="auth-main">
        <div class="auth-container">
            <!-- Left Side - Info -->
            <div class="auth-info">
                <div class="auth-info-content">
                    <h1>Admin Portal</h1>
                    <p>Access the EduBot administration dashboard to manage student data, announcements, and system settings.</p>
                    
                    <div class="auth-features">
                        <div class="auth-feature">
                            <span class="feature-icon">👥</span>
                            <div>
                                <h3>Manage Students</h3>
                                <p>Add, edit, and manage student records and information</p>
                            </div>
                        </div>
                        <div class="auth-feature">
                            <span class="feature-icon">📊</span>
                            <div>
                                <h3>Analytics Dashboard</h3>
                                <p>View system usage statistics and student engagement</p>
                            </div>
                        </div>
                        <div class="auth-feature">
                            <span class="feature-icon">⚙️</span>
                            <div>
                                <h3>System Settings</h3>
                                <p>Configure chatbot responses and system preferences</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="auth-form-container">
                <div class="auth-form-card">
                    <div class="auth-header">
                        <h2>Admin Login</h2>
                        <p>Sign in to access the admin dashboard</p>
                    </div>

                    <form class="auth-form" id="adminLoginForm">
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input 
                                type="email" 
                                id="email" 
                                name="email" 
                                placeholder="<EMAIL>"
                                required
                            >
                        </div>

                        <div class="form-group">
                            <label for="password">Password</label>
                            <div class="password-input">
                                <input 
                                    type="password" 
                                    id="password" 
                                    name="password" 
                                    placeholder="Enter your password"
                                    required
                                >
                                <button type="button" class="password-toggle" id="passwordToggle">
                                    <span class="eye-icon">👁️</span>
                                </button>
                            </div>
                        </div>

                        <div class="form-options">
                            <label class="checkbox-container">
                                <input type="checkbox" id="rememberMe">
                                <span class="checkmark"></span>
                                Remember me
                            </label>
                            <a href="#" class="forgot-password">Forgot password?</a>
                        </div>

                        <button type="submit" class="btn btn-primary auth-submit">
                            Sign In
                            <span class="btn-note">(UI Only - No Functionality Yet)</span>
                        </button>
                    </form>

                    <div class="auth-divider">
                        <span>or</span>
                    </div>

                    <div class="auth-alternatives">
                        <button class="btn btn-secondary auth-alt-btn" id="demoLogin">
                            <span class="btn-icon">🔍</span>
                            View Demo Dashboard
                        </button>
                    </div>

                    <div class="auth-footer">
                        <p>Not an admin? <a href="student-login.html">Student Login</a></p>
                        <p>Need help? <a href="../index.html#contact">Contact Support</a></p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Background Elements -->
    <div class="auth-bg-elements">
        <div class="bg-circle bg-circle-1"></div>
        <div class="bg-circle bg-circle-2"></div>
        <div class="bg-circle bg-circle-3"></div>
    </div>

    <script src="../js/script.js"></script>
    <script src="../js/auth.js"></script>
</body>
</html>
