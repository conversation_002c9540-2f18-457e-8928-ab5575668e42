<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - EduBot</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/dashboard.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="dashboard-body">
    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <h2>EduBot</h2>
                <span class="admin-badge">Admin</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <span class="toggle-icon">←</span>
            </button>
        </div>

        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item active">
                    <a href="#dashboard" class="nav-link">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#students" class="nav-link">
                        <span class="nav-icon">👥</span>
                        <span class="nav-text">Manage Students</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#data" class="nav-link">
                        <span class="nav-icon">💾</span>
                        <span class="nav-text">Manage Data</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#chatbot" class="nav-link">
                        <span class="nav-icon">🤖</span>
                        <span class="nav-text">Chatbot Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#analytics" class="nav-link">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">Analytics</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#settings" class="nav-link">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">Settings</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="sidebar-footer">
            <div class="admin-profile">
                <div class="profile-avatar">👨‍💼</div>
                <div class="profile-info">
                    <h4>Admin User</h4>
                    <p>Administrator</p>
                </div>
            </div>
            <button class="btn btn-secondary logout-btn" id="logoutBtn">
                <span class="nav-icon">🚪</span>
                Logout
            </button>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Top Navigation -->
        <header class="top-nav">
            <div class="top-nav-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
                <h1 class="page-title">Dashboard</h1>
            </div>
            <div class="top-nav-right">
                <div class="nav-actions">
                    <button class="action-btn" title="Notifications">
                        <span class="action-icon">🔔</span>
                        <span class="notification-badge">3</span>
                    </button>
                    <button class="action-btn" title="Messages">
                        <span class="action-icon">💬</span>
                    </button>
                    <button class="action-btn" title="Help">
                        <span class="action-icon">❓</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Welcome Section -->
            <section class="welcome-section">
                <div class="welcome-card">
                    <div class="welcome-content">
                        <h2>Welcome back, Admin! 👋</h2>
                        <p>Here's what's happening with EduBot today.</p>
                    </div>
                    <div class="welcome-stats">
                        <div class="stat-item">
                            <span class="stat-number">1,234</span>
                            <span class="stat-label">Active Students</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">89</span>
                            <span class="stat-label">Queries Today</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Stats Cards -->
            <section class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-icon">👥</span>
                            <h3>Total Students</h3>
                        </div>
                        <div class="stat-value">1,234</div>
                        <div class="stat-change positive">+12 this week</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-icon">💬</span>
                            <h3>Chat Queries</h3>
                        </div>
                        <div class="stat-value">2,567</div>
                        <div class="stat-change positive">+23% from last month</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-icon">📊</span>
                            <h3>Success Rate</h3>
                        </div>
                        <div class="stat-value">94.2%</div>
                        <div class="stat-change positive">+2.1% improvement</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-icon">⚡</span>
                            <h3>Response Time</h3>
                        </div>
                        <div class="stat-value">1.2s</div>
                        <div class="stat-change negative">+0.3s slower</div>
                    </div>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="actions-section">
                <h2 class="section-title">Quick Actions</h2>
                <div class="actions-grid">
                    <button class="action-card" data-action="add-student">
                        <span class="action-card-icon">➕</span>
                        <h3>Add New Student</h3>
                        <p>Register a new student in the system</p>
                    </button>
                    
                    <button class="action-card" data-action="manage-data">
                        <span class="action-card-icon">📝</span>
                        <h3>Update Data</h3>
                        <p>Modify fee, marks, or timetable information</p>
                    </button>
                    
                    <button class="action-card" data-action="view-analytics">
                        <span class="action-card-icon">📈</span>
                        <h3>View Analytics</h3>
                        <p>Check detailed usage and performance reports</p>
                    </button>
                    
                    <button class="action-card" data-action="chatbot-settings">
                        <span class="action-card-icon">🤖</span>
                        <h3>Configure Chatbot</h3>
                        <p>Adjust chatbot responses and behavior</p>
                    </button>
                </div>
            </section>

            <!-- Recent Activity -->
            <section class="activity-section">
                <h2 class="section-title">Recent Activity</h2>
                <div class="activity-card">
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon">👤</div>
                            <div class="activity-content">
                                <p><strong>New student registered:</strong> John Doe (ID: ST2024001)</p>
                                <span class="activity-time">2 hours ago</span>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon">💬</div>
                            <div class="activity-content">
                                <p><strong>High query volume:</strong> 45 fee-related questions in the last hour</p>
                                <span class="activity-time">3 hours ago</span>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon">📊</div>
                            <div class="activity-content">
                                <p><strong>Data updated:</strong> Semester exam results uploaded</p>
                                <span class="activity-time">5 hours ago</span>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon">⚙️</div>
                            <div class="activity-content">
                                <p><strong>System maintenance:</strong> Database optimization completed</p>
                                <span class="activity-time">1 day ago</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="activity-footer">
                        <button class="btn btn-secondary">View All Activity</button>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Overlay for mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <script src="../js/script.js"></script>
    <script src="../js/dashboard.js"></script>
</body>
</html>
